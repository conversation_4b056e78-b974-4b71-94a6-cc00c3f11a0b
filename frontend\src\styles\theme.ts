// 设计系统主题配置
export const theme = {
  // 色彩系统
  colors: {
    // 主色调 - 书籍蓝
    primary: {
      50: '#e6f4ff',
      100: '#bae0ff',
      200: '#91caff',
      300: '#69b1ff',
      400: '#4096ff',
      500: '#1677ff', // 主色
      600: '#0958d9',
      700: '#003eb3',
      800: '#002c8c',
      900: '#001d66'
    },

    // 辅助色 - 温暖橙
    secondary: {
      50: '#fff7e6',
      100: '#ffe7ba',
      200: '#ffd591',
      300: '#ffc069',
      400: '#ffab40',
      500: '#fa8c16', // 辅助色
      600: '#d46b08',
      700: '#ad4e00',
      800: '#873800',
      900: '#612500'
    },

    // 功能色
    success: '#52c41a',
    warning: '#faad14',
    error: '#ff4d4f',
    info: '#1677ff',

    // 中性色
    textPrimary: '#262626',
    textSecondary: '#595959',
    textTertiary: '#8c8c8c',
    textDisabled: '#bfbfbf',
    border: '#d9d9d9',
    borderLight: '#f0f0f0',
    background: '#fafafa',
    backgroundSecondary: '#f5f5f5',
    white: '#ffffff',

    // 灰度色阶
    gray: {
      50: '#fafafa',
      100: '#f5f5f5',
      200: '#f0f0f0',
      300: '#d9d9d9',
      400: '#bfbfbf',
      500: '#8c8c8c',
      600: '#595959',
      700: '#434343',
      800: '#262626',
      900: '#1f1f1f',
    },

    // 渐变色系统
    gradients: {
      primary: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
      secondary: 'linear-gradient(135deg, #fa709a 0%, #fee140 100%)',
      success: 'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)',
      warning: 'linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%)',
      error: 'linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%)',
      cool: 'linear-gradient(135deg, #a8edea 0%, #fed6e3 100%)',
      warm: 'linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%)',
      sunset: 'linear-gradient(135deg, #ff9a9e 0%, #fad0c4 100%)',
      ocean: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
      forest: 'linear-gradient(135deg, #134e5e 0%, #71b280 100%)',
      royal: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
      cosmic: 'linear-gradient(135deg, #fa709a 0%, #fee140 100%)',
    },

    // 品牌色彩
    brand: {
      bookBlue: '#1677ff',
      bookOrange: '#fa8c16',
      bookGreen: '#52c41a',
      bookPurple: '#722ed1',
      bookRed: '#ff4d4f',
    }
  },
  
  // 字体系统
  typography: {
    fontFamily: {
      chinese: "'PingFang SC', 'Microsoft YaHei', sans-serif",
      english: "'Segoe UI', 'Roboto', sans-serif",
      base: "'PingFang SC', 'Microsoft YaHei', 'Segoe UI', 'Roboto', sans-serif"
    },
    fontSize: {
      xs: '12px',
      sm: '14px',
      base: '16px',
      lg: '18px',
      xl: '20px',
      '2xl': '24px',
      '3xl': '28px',
      '4xl': '32px'
    },
    lineHeight: {
      xs: '16px',
      sm: '20px',
      base: '24px',
      lg: '26px',
      xl: '28px',
      '2xl': '32px',
      '3xl': '36px',
      '4xl': '40px'
    },
    fontWeight: {
      normal: 400,
      medium: 500,
      semibold: 600,
      bold: 700
    }
  },
  
  // 间距系统
  spacing: {
    xs: '4px',
    sm: '8px',
    md: '16px',
    lg: '24px',
    xl: '32px',
    '2xl': '48px',
    '3xl': '64px'
  },
  
  // 圆角
  borderRadius: {
    none: '0',
    sm: '4px',
    base: '6px',
    md: '8px',
    lg: '12px',
    xl: '16px',
    full: '50%'
  },
  
  // 阴影
  boxShadow: {
    sm: '0 1px 2px rgba(0, 0, 0, 0.05)',
    base: '0 2px 8px rgba(0, 0, 0, 0.1)',
    md: '0 4px 12px rgba(0, 0, 0, 0.15)',
    lg: '0 8px 24px rgba(0, 0, 0, 0.2)',
    xl: '0 12px 32px rgba(0, 0, 0, 0.25)'
  },
  
  // 响应式断点
  breakpoints: {
    xs: '0px',
    sm: '576px',
    md: '768px',
    lg: '992px',
    xl: '1200px',
    xxl: '1600px'
  },
  
  // 媒体查询
  mediaQueries: {
    xs: '@media (min-width: 0px)',
    sm: '@media (min-width: 576px)',
    md: '@media (min-width: 768px)',
    lg: '@media (min-width: 992px)',
    xl: '@media (min-width: 1200px)',
    xxl: '@media (min-width: 1600px)'
  },
  
  // 组件尺寸
  sizes: {
    button: {
      sm: { height: '32px', padding: '0 12px', fontSize: '14px' },
      base: { height: '40px', padding: '0 16px', fontSize: '16px' },
      lg: { height: '48px', padding: '0 20px', fontSize: '18px' }
    },
    input: {
      sm: { height: '32px', padding: '0 8px', fontSize: '14px' },
      base: { height: '40px', padding: '0 12px', fontSize: '16px' },
      lg: { height: '48px', padding: '0 16px', fontSize: '18px' }
    }
  },
  
  // 动画
  animation: {
    duration: {
      fast: '150ms',
      base: '300ms',
      slow: '500ms'
    },
    easing: {
      ease: 'ease',
      easeIn: 'ease-in',
      easeOut: 'ease-out',
      easeInOut: 'ease-in-out'
    }
  },

  // Z-index层级
  zIndex: {
    hide: -1,
    auto: 'auto',
    base: 0,
    docked: 10,
    dropdown: 1000,
    sticky: 1100,
    banner: 1200,
    overlay: 1300,
    modal: 1400,
    popover: 1500,
    skipLink: 1600,
    toast: 1700,
    tooltip: 1800
  }
};

// Ant Design 主题配置
export const antdTheme = {
  token: {
    // 主色
    colorPrimary: theme.colors.primary[500],
    colorSuccess: theme.colors.success,
    colorWarning: theme.colors.warning,
    colorError: theme.colors.error,
    colorInfo: theme.colors.info,

    // 字体
    fontFamily: theme.typography.fontFamily.base,
    fontSize: 14,

    // 圆角
    borderRadius: 6,

    // 间距
    padding: 16,
    margin: 16,

    // 阴影
    boxShadow: theme.boxShadow.base,
    boxShadowSecondary: theme.boxShadow.sm,

    // 动画
    motionDurationFast: theme.animation.duration.fast,
    motionDurationMid: theme.animation.duration.base,
    motionDurationSlow: theme.animation.duration.slow
  },

  components: {
    Button: {
      borderRadius: 6,
      controlHeight: 40,
      paddingContentHorizontal: 16,
      fontWeight: 500
    },

    Input: {
      borderRadius: 6,
      controlHeight: 40,
      paddingInline: 12
    },

    Card: {
      borderRadius: 8,
      paddingLG: 24,
      boxShadowTertiary: theme.boxShadow.sm
    },

    Table: {
      borderRadius: 8,
      headerBg: theme.colors.backgroundSecondary,
      headerColor: theme.colors.textPrimary
    },

    Menu: {
      borderRadius: 6,
      itemBorderRadius: 4
    },

    Modal: {
      borderRadius: 12
    },

    Drawer: {
      borderRadius: 0
    }
  }
};

export type Theme = typeof theme;
